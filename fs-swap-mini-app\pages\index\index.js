const api = require('../../config/api.js')
const util = require('../../utils/util.js')
const app = getApp()
const userUtils = require('../../utils/user')
const systemInfoService = require('../../services/systemInfo.js')
const localResidentialService = require('../../services/localResidential.js')
const globalComponents = require('../../utils/globalComponents')

Page(Object.assign({
  data: {
    searchValue: '',
    searchKeyword: '',
    statusBarHeight: 0,
    navBarHeight: 0,
    residentialName: '未绑定小区',
    // 登录状态
    hasLogin: false,
    // 轮播图数据
    banners: [],
    // 碳豆用户数据
    carbonUsers: [],
    myRanking: null,
    // 商品列表数据
    swapItems: [],
    // 瀑布流布局数据
    leftColumnItems: [],
    rightColumnItems: [],
    // 分页参数
    pageNum: 1,
    pageSize: 15,
    hasMore: true,
    // 预加载相关
    preloadThreshold: 5,
    isPreloading: false,
    // 排序参数
    activeTab: 0, // 激活的标签页，0: 综合, 1: 价格排序, 2: 发布时间
    sortType: 0, // 排序类型：0-默认，1-价格升序，2-价格降序，3-时间升序，4-时间降序
    // 页面状态控制
    isFirstLoad: true, // 是否是首次加载
    lastTabTapTime: 0, // 上次点击tab的时间戳
    // 分离的加载状态
    productLoading: false, // 商品列表加载状态
    initialLoading: false, // 初始数据加载状态
    isRefreshing: false, // 下拉刷新状态
  },

  onLoad: function () {
    console.log('首页开始加载')

    // 更新UI相关信息
    this.updateNavBarInfo()

    // 设置默认排序状态
    this.setData({
      activeTab: 0,
      sortType: 0,
    })

    // 开启分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
    })

    // 使用新的页面初始化模式
    this.initAndLoadData()
  },

  /**
   * 初始化并加载数据 设置监听，等小区状态ready后再加载数据
   */
  initAndLoadData() {
    console.log('首页开始初始化...')

    // 更新登录状态
    this.setData({
      hasLogin: app.safeGetGlobalData('hasLogin', false)
    })

    // 更新小区名称显示
    this.updateResidentialName()

    // 设置小区状态监听（用于小区选择后的数据刷新）
    this.setupResidentialWatcher()

    console.log('首页初始化完成')
  },

  /**
   * 简化版：只在有小区状态时才加载数据
   */
  loadAllData() {
    console.log('开始加载所有数据...')

    // 防止重复加载
    if (this.data.isRefreshing || this.data.initialLoading) {
      console.log('正在加载中，跳过重复请求')
      return
    }

    // 显示刷新动画
    this.setData({ isRefreshing: true })

    // 检查是否有小区状态
    const hasResidential = app.safeGetGlobalData('hasResidential', false)
    if (!hasResidential) {
      console.log('暂无小区状态，跳过数据加载')
      // 添加延迟关闭刷新动画，提供更好的用户体验
      setTimeout(() => {
        this.setData({ isRefreshing: false })
      }, 800)
      return
    }

    try {
      // 显示初始加载状态
      this.setData({ initialLoading: true })

      // 并行加载所有数据
      // 此时小区状态已ready，请求拦截器会正确带上小区参数
      Promise.all([
        this.loadBannerList(),
        this.loadProductList(true),
        this.loadRanking()
      ]).then(() => {
        console.log('数据加载完成')
      }).catch((error) => {
        console.error('数据加载失败:', error)
        // 5013错误由request拦截器处理，这里不需要额外处理
        // 其他错误显示通用提示
        if (error.code !== 5013) {
          wx.showToast({
            title: '数据加载失败',
            icon: 'none'
          })
        }
      }).finally(() => {
        // 添加延迟关闭刷新动画，提供更好的用户体验
        setTimeout(() => {
          this.setData({
            initialLoading: false,
            isRefreshing: false
          })
        }, 500)
      })

    } catch (error) {
      console.error('数据加载异常:', error)
      // 添加延迟关闭刷新动画，提供更好的用户体验
      setTimeout(() => {
        this.setData({
          initialLoading: false,
          isRefreshing: false
        })
      }, 800)
    }
  },

  /**
   * 设置小区状态监听
   */
  setupResidentialWatcher() {
    console.log('设置小区状态监听')

    // 检查app是否有watch方法
    if (typeof app.watch !== 'function') {
      console.warn('App watch method not ready')
      return
    }

    // 监听小区变更
    this.residentialWatcherId = app.watch('residential', (newResidential, oldResidential) => {
      console.log('小区状态变更:', { old: oldResidential, new: newResidential })

      // 更新页面状态
      this.setData({
        hasResidential: newResidential !== null,
        residentialName: newResidential ? newResidential.name : '选择小区'
      })

      if (newResidential) {
        console.log('小区已选择，重新加载数据')
        this.loadAllData()
      } else {
        console.log('小区已清除，清空数据')
        this.clearData()
      }
    })

    // 初始化时如果已经有小区，立即加载
    const currentResidential = app.safeGetGlobalData('currentResidential')
    if (currentResidential) {
      console.log('初始化时已有小区，立即加载数据:', currentResidential)
      this.setData({
        hasResidential: true,
        residentialName: currentResidential.name
      })
      this.loadAllData()
    } else {
      console.log('初始化时暂无小区，等待小区状态变更')
    }
  },

  /**
   * 清空数据
   */
  clearData() {
    this.setData({
      banners: [],
      swapItems: [],
      leftColumnItems: [],
      rightColumnItems: [],
      carbonUsers: [],
      myRanking: null,
      pageNum: 1,
      hasMore: true
    })
  },

  onShow() {
    console.log('onShow 触发')

    // 如果不是首次加载，说明是从其他页面返回
    if (!this.data.isFirstLoad) {
      console.log('从其他页面返回')

      // 更新小区名称显示
      this.updateResidentialName()

      // 如果已经有数据，就不重新加载，保持当前滚动位置
      if (this.data.swapItems.length > 0) {
        console.log('已有数据，跳过重新加载')
        return
      }

      // 只有在没有数据且有小区的情况下才加载数据
      const hasResidential = app.safeGetGlobalData('hasResidential', false)
      if (hasResidential) {
        console.log('无数据且有小区状态，加载数据')
        this.loadAllData()
      } else {
        console.log('暂无小区状态，等待小区状态变更')
      }
    }

    // 标记为非首次加载
    this.setData({
      isFirstLoad: false
    })

    // 延迟检查是否需要预加载
    setTimeout(() => {
      this.checkPreload()
    }, 1000)
  },

  // 注意：showLogin 和 hideLogin 方法已通过 globalComponentsMixin 提供

  // 确保登录并执行回调的辅助方法
  ensureLogin(callback) {
    const app = getApp()
    const hasLogin = app.safeGetGlobalData('hasLogin', false)

    if (hasLogin) {
      // 已登录，直接执行回调
      if (typeof callback === 'function') {
        callback()
      }
      return true
    } else {
      // 未登录，显示登录组件
      this.showLogin(true) // 使用混入的方法
      return false
    }
  },

  // 更新小区名称的方法
  /**
   * 更新小区名称显示
   */
  updateResidentialName() {
    // 优先显示用户绑定的小区
    const userInfo = app.safeGetGlobalData('userInfo')
    if (userInfo && userInfo.currentResidentialName) {
      this.setData({
        residentialName: userInfo.currentResidentialName,
      })
      return;
    }

    // 其次显示本地选择的小区
    const currentResidential = app.safeGetGlobalData('currentResidential')
    if (currentResidential && currentResidential.name) {
      this.setData({
        residentialName: currentResidential.name,
      })
      return;
    }

    // 最后显示默认值
    this.setData({
      residentialName: '选择小区',
    })
  },

  // 更新导航栏和状态栏高度信息
  updateNavBarInfo: function () {
    const navBarInfo = util.getNavBarInfo()
    this.setData({
      statusBarHeight: navBarInfo.statusBarHeight,
      navBarHeight: navBarInfo.navBarHeight,
      searchBarTop: navBarInfo.searchBarTop,
    })
  },

  // 加载轮播图数据
  loadBannerList: function() {
    const that = this
    api.getBannerList().then(res => {
      if (res && res.code === 200 && res.data) {
        // 处理轮播图数据
        const banners = res.data.map(item => {
          // 根据linkType处理跳转链接
          let link = ''
          switch (item.linkType) {
            case '1': // 商品
              link = '/pages/item/detail?id=' + item.linkValue
              break
            case '2': // 外链
              link = item.linkValue // 外链需要特殊处理，这里简化处理
              break
            case '3': // 页面
              link = item.linkValue // 小程序内页面路径
              break
            default:
              link = ''
          }

          return {
            id: item.id,
            title: item.title,
            image: item.image,
            link: link
          }
        })

        that.setData({
          banners: banners
        })
      } else {
        // 如果返回数据为空或格式错误，设置空数组
        that.setData({
          banners: []
        })
      }
    }).catch((err) => {
      // 轮播图加载失败，设置空数组
      this.setData({
        banners: []
      })
    })
  },

  // 点击轮播图
  onBannerTap: function (event) {
    const bannerId = event.currentTarget.dataset.id
    const banner = this.data.banners.find((item) => item.id === bannerId)

    if (banner && banner.link) {
      // 判断是否为外部链接
      if (banner.link.startsWith('http')) {
        // 显示确认对话框
        wx.showModal({
          title: '外部链接',
          content: '是否复制链接到剪贴板？',
          confirmText: '复制链接',
          cancelText: '取消',
          success: function(res) {
            if (res.confirm) {
              // 复制链接到剪贴板
              wx.setClipboardData({
                data: banner.link,
                success: function() {
                  wx.showToast({
                    title: '链接已复制，请在浏览器中打开',
                    icon: 'none'
                  })
                }
              })
            }
          }
        })
      } else {
        // 内部页面跳转
        wx.navigateTo({
          url: banner.link,
        })
      }
    }
  },

  // 搜索框输入变化
  onSearch: function (e) {
    const keyword = e.detail.value || e.detail
    
    if (keyword && keyword.trim()) {
      // 更新搜索关键词并加载商品列表
      this.setData({
        searchKeyword: keyword.trim(),
        productList: [], // 清空当前列表
        page: 1,
        hasMore: true,
      })

      // 加载搜索结果
      this.loadProductList(true)
    }
  },

  // 搜索输入处理
  onSearchInput: function (e) {
    const value = e.detail.value
    this.setData({
      searchValue: value
    })
    
    // 实时搜索（可选，这里简单处理）
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
    
    // 延迟搜索，避免频繁请求
    this.searchTimer = setTimeout(() => {
      if (value && value.trim()) {
        this.performSearch(value.trim())
      } else {
        // 如果搜索词为空，重置到默认列表
        this.resetToDefaultList()
      }
    }, 500)
  },

  // 搜索确认
  onSearchConfirm: function (event) {
    const keyword = event.detail.value || this.data.searchValue

    if (keyword && keyword.trim()) {
      this.performSearch(keyword.trim())
    }
  },

  // 执行搜索
  performSearch: function (keyword) {
    console.log('执行搜索:', keyword)
    
    this.setData({
      searchKeyword: keyword,
      pageNum: 1,
      hasMore: true,
      swapItems: [],
      leftColumnItems: [],
      rightColumnItems: []
    })

    // 加载搜索结果
    this.loadProductList(true)
  },

  // 清空搜索
  onSearchClear: function () {
    this.setData({
      searchValue: ''
    })
    this.resetToDefaultList()
  },

  // 取消搜索
  onSearchCancel: function () {
    this.setData({
      searchValue: ''
    })
    this.resetToDefaultList()
  },

  // 重置到默认列表
  resetToDefaultList: function () {
    this.setData({
      searchKeyword: '',
      pageNum: 1,
      hasMore: true,
      swapItems: [],
      leftColumnItems: [],
      rightColumnItems: []
    })
    
    // 重新加载默认商品列表
    this.loadProductList(true)
  },

  // 切换标签页
  onTabChange: function (event) {
    const index = parseInt(event.currentTarget.dataset.index)
    let sortType = 0

    // 如果点击的是当前已激活的标签，切换排序方向
    if (index === this.data.activeTab) {
      switch (index) {
        case 1: // 价格排序：在升序和降序之间切换
          sortType = this.data.sortType === 1 ? 2 : 1
          break
        case 2: // 发布时间：在升序和降序之间切换
          sortType = this.data.sortType === 3 ? 4 : 3
          break
        default: // 综合排序
          sortType = 0
      }
    } else {
      // 如果点击的是新标签，设置默认排序方向
      switch (index) {
        case 1: // 价格排序：默认为升序
          sortType = 1
          break
        case 2: // 发布时间：默认为降序（最新的先显示）
          sortType = 4
          break
        default: // 综合排序
          sortType = 0
      }
    }

    // 先更新排序状态，不立即刷新
    this.setData({
      activeTab: index,
      sortType: sortType,
    })

    // 使用节流函数延迟执行刷新操作，避免频繁刷新导致闪烁
    if (this.loadProductListTimer) {
      clearTimeout(this.loadProductListTimer)
    }

    this.loadProductListTimer = setTimeout(() => {
      this.loadProductList(true)
    }, 50)
  },

  // 加载商品列表
  async loadProductList(refresh = false) {
    console.log(`🔄 loadProductList 被调用，refresh: ${refresh}`)

    // 防止重复加载
    if (this.data.productLoading) {
      console.log('⚠️ 商品列表已在加载中，跳过重复请求')
      return Promise.resolve()
    }

    // 如果是刷新操作，重置分页参数
    if (refresh) {
      console.log('🔄 刷新模式：重置分页参数')
      this.setData({
        pageNum: 1,
        hasMore: true,
        swapItems: [],
        leftColumnItems: [],
        rightColumnItems: [],
      })
    }

    // 检查是否还有更多数据
    if (!this.data.hasMore && !refresh) {
      console.log('⚠️ 没有更多数据，跳过加载')
      return Promise.resolve()
    }

    console.log(`📦 开始加载商品列表，页码: ${this.data.pageNum}`)
    this.setData({
      productLoading: true,
    })

    try {
      // 构建排序参数，使用后端TableSupport期望的格式
      let orderByColumn = 'createTime'; // 默认按创建时间排序（使用驼峰命名）
      let isAsc = 'desc'; // 默认降序

      // 根据sortType设置排序参数
      switch (this.data.sortType) {
        case 1: // 价格升序
          orderByColumn = 'price';
          isAsc = 'asc';
          break;
        case 2: // 价格降序
          orderByColumn = 'price';
          isAsc = 'desc';
          break;
        case 3: // 时间升序（最早的先显示）
          orderByColumn = 'createTime';
          isAsc = 'asc';
          break;
        case 4: // 时间降序（最新的先显示）
          orderByColumn = 'createTime';
          isAsc = 'desc';
          break;
        default: // 综合排序（默认按创建时间降序）
          orderByColumn = 'createTime';
          isAsc = 'desc';
      }

      // 获取商品列表
      // 小区参数由request拦截器自动添加，如果未选择小区会返回5013错误
      const requestParams = {
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize,
        status: 1,
        orderByColumn: orderByColumn, // 使用后端期望的参数格式
        isAsc: isAsc,
      }

      // 如果有搜索关键词，添加到请求参数中
      if (this.data.searchKeyword) {
        requestParams.description = this.data.searchKeyword
        console.log('🔍 搜索商品描述:', this.data.searchKeyword)
      }

      const res = await api.getProductList(requestParams)

      const {
        rows,
        total
      } = res

      // 使用系统信息服务处理图片路径和发布时间
      const processedRows = await Promise.all(rows.map(async (item) => {
        // 使用系统信息服务处理图片路径
        if (item.images && typeof item.images === 'string') {
          // 分割图片地址字符串，取第一个地址
          const imageArray = item.images.split(',')
          if (imageArray.length > 0) {
            // 保存原始的images字段，并添加处理后的第一张图片完整路径
            item.firstImage = await systemInfoService.processImageUrl(imageArray[0].trim())
            // 判断是否为超长图片，设置标志
            // 由于无法直接获取图片宽高，我们通过图片文件名或其他特征来模拟判断
            // 实际应用中，后端应该返回图片宽高比
            // 这里假设图片文件名中含有"long"或宽高比例大于2的为超长图片
            const imgName = imageArray[0].toLowerCase()
            if (
              imgName.includes('long') ||
              (item.imageRatio && item.imageRatio > 2) ||
              // 临时随机设置一些图片为长图，用于演示
              Math.random() > 0.7
            ) {
              item.longImage = true
            }
          }
        }

        // 处理发布时间，计算相对时间
        item.relativeTime = util.formatRelativeTime(item.createTime)

        // 使用系统信息服务处理用户头像
        if (item.avatar) {
          item.avatar = await systemInfoService.processImageUrl(item.avatar)
        } else {
          // 如果没有用户头像，使用默认头像
          item.avatar = '/static/img/default_avatar.png'
        }
        
        // 确保有用户名
        if (!item.nickname) {
          item.nickname = '趣换用户'
        }
        return item
      }))

      // 计算是否还有更多数据
      const currentTotal = refresh ? 0 : this.data.swapItems.length
      const hasMore = currentTotal + processedRows.length < total

      // 更新页面数据，如果是刷新则替换列表，否则追加到列表末尾
      this.setData({
        swapItems: refresh ?
          processedRows :
          [...this.data.swapItems, ...processedRows],
        pageNum: refresh ? 2 : this.data.pageNum + 1,
        hasMore,
        productLoading: false,
      }, () => {
        // 在setData完成后计算瀑布流布局
        this.calculateWaterfallLayout()
      })
    } catch (error) {
      // 5013错误由request拦截器处理，这里不需要额外处理
      if (error.code !== 5013) {
        wx.showToast({
          title: error.message || '加载失败',
          icon: 'none',
          duration: 2000,
        })
      }
      
      this.setData({
        productLoading: false
      })
    }
  },

  // 社区服务功能（
  onCommunityService: function () {
    this.ensureLogin(() => {
      wx.navigateTo({
        url: '/pages/community-service/index',
      })
    })
  },

  // 邻里互助功能
  onCommunityHelp: function () {
    this.ensureLogin(() => {
      wx.navigateTo({
        url: '/pages/community-help/index',
      })
    })
  },

  // 常用电话功能
  onPhoneService: function () {
    this.ensureLogin(() => {
      wx.navigateTo({
        url: '/pages/community-phone/index',
      })
    })
  },

  // 借用功能
  onBorrowFunction: function () {
    this.ensureLogin(() => {
      wx.navigateTo({
        url: '/pages/borrow/index',
      })
    })
  },

  // 查看碳豆详情
  onCarbonBeanDetail: function () {
    this.ensureLogin(() => {
      wx.navigateTo({
        url: '/pages/ranking/index',
      })
    })
  },

  // 点击碳豆排行榜用户头像
  onCarbonUserTap: function (event) {
    const userId = event.currentTarget.dataset.userId
    if (userId) {
      wx.navigateTo({
        url: `/pages/user/userProfile/userProfile?userId=${userId}`,
      })
    }
  },

  // 查看商品详情
  onItemDetail: function (event) {
    // 查看商品详情不需要登录
    const itemId = event.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/item/detail?id=' + itemId,
    })
  },

  // 返回按钮
  onNavigateBack: function () {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.switchTab({
          url: '/pages/index/index',
        })
      },
    })
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: '趣换闲置',
      path: '/pages/index/index',
    }
  },

  onShareTimeline: function () {
    return {
      title: '趣换闲置',
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshPageData() {
    console.log('刷新页面数据')

    // 只有在有小区状态时才刷新数据
    const hasResidential = app.safeGetGlobalData('hasResidential', false)
    if (hasResidential) {
      console.log('有小区状态，执行数据刷新')
      return this.loadAllData()
    } else {
      console.log('暂无小区状态，跳过数据刷新')
      return Promise.resolve()
    }
  },

  /**
   * 下拉刷新 - scroll-view 版本
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新')

    // 设置刷新状态
    this.setData({ isRefreshing: true })

    // 直接刷新所有数据
    this.refreshPageData().then(() => {
      console.log('下拉刷新完成')
    }).catch((error) => {
      console.error('下拉刷新失败:', error)
    }).finally(() => {
      // scroll-view 的下拉刷新需要设置 refresher-triggered 为 false
      // 添加延迟关闭刷新动画，提供更好的用户体验
      setTimeout(() => {
        this.setData({ isRefreshing: false })
      }, 800)
    })
  },

  // 触底加载更多
  onReachBottom: function () {
    // 检查是否需要预加载
    this.checkPreload()
    
    // 如果还有更多数据且不在加载中，直接加载
    if (this.data.hasMore && !this.data.productLoading && !this.data.isPreloading) {
      this.loadProductList();
    }
  },

  // 页面滚动监听（优化预加载体验）
  onPageScroll: function (e) {
    // 获取页面滚动信息
    const { scrollTop } = e
   
    const windowInfo = wx.getWindowInfo()
    const windowHeight = windowInfo.windowHeight
    
    // 获取页面高度（估算）
    const estimatedPageHeight = this.data.swapItems.length * 200 + 800 // 估算每个商品200px高度，加上其他内容800px
    
    // 使用智能预加载检查
    this.smartPreloadCheck(scrollTop, estimatedPageHeight, windowHeight)
    
    // 同时检查基础预加载
    if (scrollTop > 500) {
      this.checkPreload()
    }
  },

  loadRanking() {
    // 首页只显示前3名，使用limit=3
    api.getMonthlyRanking({ limit: 3 }).then(async res => {
      if (res.code === 200) {
        const rankingList = res.data.rankingList || []
        // 处理字段映射，统一使用totalSilver字段，并处理头像URL
        const processedList = await Promise.all(rankingList.map(async item => {
          let processedAvatar = '/static/img/default_avatar.png'
          if (item.avatar) {
            processedAvatar = await systemInfoService.processImageUrl(item.avatar)
          }
          
          return {
            id: item.userId,
            rank: item.rank,
            reward:item.reward,
            name: item.nickname || `用户${item.userId}`,
            nickname: item.nickname,
            avatar: processedAvatar,
            totalSilver: item.totalSilver || 0,
            residentialId: item.residentialId
          }
        }))
        
        this.setData({
          carbonUsers: processedList,
          myRanking: res.data.myRanking
        })
      }
    }).catch(err => {
      // 首页不显示错误提示，静默处理
    })
  },

  // 计算瀑布流布局
  calculateWaterfallLayout() {
    const { swapItems } = this.data
    const leftColumnItems = []
    const rightColumnItems = []

    // 如果只有一个商品，直接放在左列
    if (swapItems.length === 1) {
      leftColumnItems.push(swapItems[0])
    } else {
      // 多个商品时使用交替分配
      swapItems.forEach((item, index) => {
        if (index % 2 === 0) {
          leftColumnItems.push(item)
        } else {
          rightColumnItems.push(item)
        }
      })
    }

    this.setData({
      leftColumnItems,
      rightColumnItems
    })

    // 检查是否需要预加载
    this.checkPreload()
  },

  // 检查是否需要预加载
  checkPreload() {
    const { swapItems, hasMore, productLoading, isPreloading, preloadThreshold } = this.data
    
    // 如果还有更多数据且不在加载中，检查是否需要预加载
    if (hasMore && !productLoading && !isPreloading) {
      // 计算当前显示的商品总数
      const currentItemCount = swapItems.length
      
      // 如果当前商品数量少于阈值，开始预加载
      if (currentItemCount <= preloadThreshold) {
        this.setData({
          isPreloading: true
        })
        
        // 延迟一小段时间后开始加载，避免频繁请求
        setTimeout(() => {
          this.loadProductList(false).then(() => {
            this.setData({
              isPreloading: false
            })
          }).catch(() => {
            this.setData({
              isPreloading: false
            })
          })
        }, 200) // 减少延迟时间，提升响应速度
      }
    }
  },

  // 智能预加载检查（基于滚动行为）
  smartPreloadCheck(scrollTop, scrollHeight, windowHeight) {
    const { hasMore, productLoading, isPreloading } = this.data
    
    // 如果还有更多数据且不在加载中
    if (hasMore && !productLoading && !isPreloading) {
      // 计算距离底部的距离
      const distanceToBottom = scrollHeight - scrollTop - windowHeight
      
      // 如果距离底部小于屏幕高度的2倍，开始预加载（提前更多距离）
      if (distanceToBottom < windowHeight * 2) {
        this.setData({
          isPreloading: true
        })
        
        setTimeout(() => {
          this.loadProductList(false).then(() => {
            this.setData({
              isPreloading: false
            })
          }).catch(() => {
            this.setData({
              isPreloading: false
            })
          })
        }, 100)
      }
    }
  },

  // 加载更多按钮点击事件
  onLoadMoreClick: function () {
    // 用户点击加载更多，触发登录检查
    this.loadProductList(false)
  },

  // 页面卸载时取消监听
  onUnload: function () {
    console.log('首页卸载，清理资源')

    // 清除小区状态监听器
    if (this.residentialWatcherId && typeof app.unwatch === 'function') {
      app.unwatch(this.residentialWatcherId)
      this.residentialWatcherId = null
    }

    // 清除可能存在的回调
    if (app.residentialCallback) {
      app.residentialCallback = null
    }

    // 清除任何可能存在的定时器
    if (this.loadProductListTimer) {
      clearTimeout(this.loadProductListTimer)
      this.loadProductListTimer = null
    }

    // 清除搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
      this.searchTimer = null
    }
  },

  // 点击底部 tabBar 首页按钮时触发
  onTabItemTap: function(item) {
    // 如果已经在首页
    if (item.pagePath === 'pages/index/index') {
      // 如果页面已经处于活跃状态，说明用户在首页点击了首页tab，触发刷新
      if (this.data.isPageActive) {
        console.log('在首页点击首页tab，触发刷新')

        // 防止重复加载：如果正在刷新中，则跳过
        if (this.data.isRefreshing || this.data.initialLoading) {
          console.log('正在加载中，跳过重复刷新')
          return
        }

        // 重置搜索状态
        this.setData({
          searchValue: '',
          searchKeyword: '',
          activeTab: 0,
          sortType: 0
        })

        // 触发刷新，相当于下拉刷新的效果
        this.refreshPageData()
      } else {
        // 页面不是活跃状态，说明是从其他页面切换过来，不自动刷新
        console.log('从其他页面切换到首页，不自动刷新')
      }
    }
  },

  // 选择小区位置
  onSelectLocation: function () {
    // 如果小区认证状态为"1"，则不执行任何操作
    const userInfo = app.safeGetGlobalData('userInfo')
    if(userInfo) {
      const residentialCertificationStatus = app.globalData.userInfo.residentialCertificationStatus
      if (!!residentialCertificationStatus && residentialCertificationStatus === "1") {
        return
      }
    }
   
    
    wx.navigateTo({
      url: '/pages/residential-select/index',
    })
  },

  // 点击小区名称选择小区
  onResidentialNameTap: function () {
    // 如果小区认证状态为"1"，则不执行任何操作
    const residentialCertificationStatus = app.globalData.residentialCertificationStatus
    if (residentialCertificationStatus === "1") {
      return
    }
    
    wx.navigateTo({
      url: '/pages/residential-select/index',
    })
  },

  // 重写混入的小区认证确认方法，添加页面特定逻辑
  onConfirmResidentialAuth: function(e) {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onConfirmResidentialAuth.call(this, e)

    // 添加页面特定的处理逻辑：认证成功后更新小区名称
    setTimeout(() => {
      this.updateResidentialName()
    }, 100)
  },

  // 注意：showResidentialAuth 和 onCloseResidentialAuth 方法已通过 globalComponentsMixin 提供

  // 重写混入的登录成功方法，添加页面特定逻辑
  onLoginSuccess: function() {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onLoginSuccess.call(this)

    // 添加页面特定的处理逻辑
    console.log('首页：登录成功，更新页面状态')

    // 更新登录状态
    this.setData({
      hasLogin: true
    })

    // 更新小区名称显示
    this.updateResidentialName()

    // 重新加载数据
    this.loadAllData()
  },

  // 重写混入的登录失败方法，添加页面特定逻辑
  onLoginFail: function(e) {
    // 调用父类方法
    globalComponents.globalComponentsMixin.onLoginFail.call(this, e)

    // 添加页面特定的处理逻辑
    console.log('首页：登录失败')
  },



}, globalComponents.globalComponentsMixin))