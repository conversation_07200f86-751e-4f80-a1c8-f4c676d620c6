const messageService = require('../../services/message')
const systemInfoService = require('../../services/systemInfo')
const { formatTime } = require('../../utils/dateUtil')
const userUtils = require('../../utils/user')
const app = getApp()

Page({
  data: {
    // 登录状态
    hasLogin: false,
    // 会话列表
    conversationList: [],
    // 系统消息（作为特殊的会话项）
    systemMessages: [],
    // 加载状态
    isLoading: false,
    isRefreshing: false,
    // 未读消息总数
    unreadCount: 0,
    // 搜索关键词
    searchKeyword: '',
    // 简单的刷新指示器
    showRefreshIndicator: false,
    // 页面状态控制
    isFirstLoad: true, // 是否是首次加载
  },

  // 简单定时器
  refreshTimer: null,
  refreshInterval: 30000, // 30秒刷新一次

  onLoad() {
    // 初始化页面登录状态 - 使用基于监听的登录初始化方法
    this.loginController = userUtils.initPageLogin(this, {
      onLoginSuccess: () => {
        // 登录成功后加载数据
        this.handleLoginSuccess()
      },
      allowGuestMode: true, // 消息页面允许游客模式，不直接弹出登录框
      guestModeCallback: () => {
        // 游客模式下的处理
        console.log('消息页面游客模式：显示登录提示')
        this.setData({ hasLogin: false })
        this.showGuestMode()
      },
      onLoginRequired: () => {
        // 需要显示登录框时的处理
        console.log('消息页面需要用户登录')
        this.setData({ hasLogin: false })
      },
      initialDelay: 50 // TabBar页面使用更短的初始延迟
    })

    // 启动自动刷新
    this.startAutoRefresh()
  },

  onShow() {
    console.log('消息页面 onShow 触发')

    // 如果不是首次加载，说明是从其他页面返回
    if (!this.data.isFirstLoad) {
      console.log('从其他页面返回')

      // 如果已经有数据且已登录，就不重新加载，保持当前滚动位置
      if (this.data.conversationList.length > 0 && app.globalData.hasLogin) {
        console.log('已有数据，跳过重新加载')
        return
      }

      // 页面显示时检查登录状态并刷新数据
      if (app.globalData.hasLogin) {
        console.log('已登录，加载数据')
        this.setData({ hasLogin: true })
        this.loadData()
      }
    }

    // 标记为非首次加载
    this.setData({
      isFirstLoad: false
    })
  },

  onHide() {
    // 页面隐藏时停止自动刷新
    this.stopAutoRefresh()
  },

  onUnload() {
    // 清除登录控制器
    if (this.loginController) {
      this.loginController.cleanup()
      this.loginController = null
    }
    
    // 停止自动刷新
    this.stopAutoRefresh()
  },

  onPullDownRefresh() {
    this.loadData().finally(() => {
      // 添加延迟关闭刷新动画，提供更好的用户体验
      setTimeout(() => {
        wx.stopPullDownRefresh()
      }, 500)
    })
  },

  // 手动触发下拉刷新动画
  manualRefresh() {
    wx.startPullDownRefresh({
      success: () => {
        console.log('手动触发下拉刷新动画')
        // 这里会自动调用 onPullDownRefresh() 方法
      },
      fail: (err) => {
        console.error('启动下拉刷新失败:', err)
        // 如果启动失败，直接调用加载方法
        this.loadData()
      }
    })
  },

  /**
   * 启动自动刷新
   */
  startAutoRefresh() {
    this.stopAutoRefresh() // 先停止之前的定时器
    
    this.refreshTimer = setInterval(() => {
      this.silentRefresh()
    }, this.refreshInterval)
    
    },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
      }
  },

  /**
   * 静默刷新（不显示loading）
   */
  async silentRefresh() {
    // 检查登录状态，未登录时不刷新数据
    if (!this.data.hasLogin) {
      return
    }

    try {
      this.setData({ showRefreshIndicator: true })
      
      // 静默获取数据
      const [conversationResult, unreadResult] = await Promise.all([
        messageService.getConversationList(),
        messageService.getUnreadCount()
      ])
      
      // 处理会话列表 - 使用systemInfoService处理头像
      const conversationList = await Promise.all((conversationResult.list || []).map(async item => {
        // 处理头像
        let avatar = '/static/img/default_avatar.png'
        if (item.avatar) {
          avatar = await systemInfoService.processImageUrl(item.avatar)
        }
        
        return {
          ...item,
          avatar: avatar,
          lastMessageTimeText: formatTime(item.lastMessageTime),
          type: 'conversation'
        }
      }))
      
      // 加载系统消息
      await this.loadSystemMessages()
      
      // 更新数据
      this.setData({
        conversationList,
        unreadCount: unreadResult.total || 0
      })
      
      // 更新徽章
      this.updateTabBarBadge()
      
    } catch (error) {
      console.error('静默刷新失败:', error)
    } finally {
      // 延迟隐藏指示器，让用户能看到刷新效果
      setTimeout(() => {
        this.setData({ showRefreshIndicator: false })
      }, 1000)
    }
  },

  /**
   * 加载所有数据
   */
  async loadData() {
    // 检查登录状态，未登录时不加载数据
    if (!this.data.hasLogin) {
      this.setData({ isLoading: false })
      return
    }

    // 防止重复加载
    if (this.data.isLoading) {
      console.log('正在加载中，跳过重复请求')
      return
    }

    try {
      this.setData({ isLoading: true })
      
      // 并行加载所有数据
      const [conversationResult, unreadResult] = await Promise.all([
        messageService.getConversationList(),
        messageService.getUnreadCount()
      ])
      
      // 处理会话列表 - 使用systemInfoService处理头像
      const conversationList = await Promise.all((conversationResult.list || []).map(async item => {
        // 处理头像
        let avatar = '/static/img/default_avatar.png'
        if (item.avatar) {
          avatar = await systemInfoService.processImageUrl(item.avatar)
        }
        
        const processedItem = {
          ...item,
          avatar: avatar,
          lastMessageTimeText: formatTime(item.lastMessageTime),
          type: 'conversation'
        }
        
        return processedItem
      }))
      
      // 加载系统消息
      await this.loadSystemMessages()
      
      // 更新数据
      this.setData({
        conversationList,
        unreadCount: unreadResult.total || 0,
        isLoading: false
      })
      
      // 更新徽章
      this.updateTabBarBadge()
      
      } catch (error) {
      console.error('加载数据失败:', error)
      this.setData({ isLoading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 加载系统消息（作为特殊的会话项）
   */
  async loadSystemMessages() {
    try {
      const systemMessages = []
      
      // 获取系统消息
      const systemResult = await messageService.getMessageList({
        type: 'system',
        pageNum: 1,
        pageSize: 1
      })
      
      if (systemResult.list.length > 0) {
        const latestSystemMsg = systemResult.list[0]
        const systemConversation = {
          conversationId: 'system',
          title: '系统通知',
          icon: 'bell-o',
          iconColor: '#3B7FFF',
          lastMessageContent: latestSystemMsg.content,
          lastMessageTime: latestSystemMsg.createTime,
          lastMessageTimeText: formatTime(latestSystemMsg.createTime),
          unreadCount: await this.getSystemUnreadCount(),
          type: 'system',
          isOfficial: true
        }
        systemMessages.push(systemConversation)
      }
      
      // 获取活动消息
      const activityResult = await messageService.getMessageList({
        type: 'activity',
        pageNum: 1,
        pageSize: 1
      })
      
      if (activityResult.list.length > 0) {
        const latestActivityMsg = activityResult.list[0]
        const activityConversation = {
          conversationId: 'activity',
          title: '活动通知',
          icon: 'gift-o',
          iconColor: '#ff9800',
          lastMessageContent: latestActivityMsg.content,
          lastMessageTime: latestActivityMsg.createTime,
          lastMessageTimeText: formatTime(latestActivityMsg.createTime),
          unreadCount: await this.getActivityUnreadCount(),
          type: 'activity',
          isOfficial: true
        }
        systemMessages.push(activityConversation)
      }
      
      this.setData({ systemMessages })
      
    } catch (error) {
      console.error('加载系统消息失败:', error)
    }
  },

  /**
   * 获取系统消息未读数量
   */
  async getSystemUnreadCount() {
    try {
      const result = await messageService.getUnreadCount()
      return result.system || 0
    } catch (error) {
      console.error('获取系统消息未读数量失败:', error)
      return 0
    }
  },

  /**
   * 获取活动消息未读数量
   */
  async getActivityUnreadCount() {
    try {
      const result = await messageService.getUnreadCount()
      return result.activity || 0
    } catch (error) {
      console.error('获取活动消息未读数量失败:', error)
      return 0
    }
  },

  /**
   * 点击会话项
   */
  onConversationTap(e) {
    const { conversation } = e.currentTarget.dataset
    
    if (conversation.type === 'system') {
      wx.navigateTo({
        url: '/pages/message/system/index'
      })
    } else if (conversation.type === 'activity') {
      wx.navigateTo({
        url: '/pages/message/activity/index'
      })
    } else {
      wx.navigateTo({
        url: `/pages/conversation/detail/index?conversationId=${conversation.conversationId}&title=${conversation.title}`
      })
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    this.filterConversations(keyword)
  },

  /**
   * 过滤会话列表
   */
  filterConversations(keyword) {
    if (!keyword.trim()) {
      this.loadData()
      return
    }
    
    const { conversationList, systemMessages } = this.data
    const allConversations = [...systemMessages, ...conversationList]
    
    const filteredList = allConversations.filter(item => 
      item.title.includes(keyword) || 
      (item.lastMessageContent && item.lastMessageContent.includes(keyword))
    )
    
    // 分离系统消息、活动消息和普通会话
    const filteredSystem = filteredList.filter(item => item.type === 'system' || item.type === 'activity')
    const filteredConversations = filteredList.filter(item => item.type === 'conversation')
    
    this.setData({
      systemMessages: filteredSystem,
      conversationList: filteredConversations
    })
  },

  /**
   * 清除搜索
   */
  clearSearch() {
    this.setData({ searchKeyword: '' })
    this.loadData()
  },

  /**
   * 更新tabBar徽章
   */
  updateTabBarBadge() {
    const { unreadCount } = this.data
    
    if (unreadCount > 0) {
      wx.setTabBarBadge({
        index: 3,
        text: unreadCount > 99 ? '99+' : String(unreadCount)
      })
    } else {
      wx.removeTabBarBadge({
        index: 3
      })
    }
  },

  /**
   * 头像加载错误处理
   */
  onAvatarError(e) {
    const { index } = e.currentTarget.dataset
    
    // 更新对应会话的头像为默认头像
    const conversationList = [...this.data.conversationList]
    if (conversationList[index]) {
      conversationList[index].avatar = '/static/img/default_avatar.png'
      
      this.setData({
        conversationList: conversationList
      })
    }
  },

  /**
   * 处理登录成功
   */
  handleLoginSuccess() {
    this.setData({
      hasLogin: true,
    })
    
    // 登录成功后加载数据
    this.loadData()
  },

  /**
   * 显示游客模式内容
   */
  showGuestMode() {
    // 游客模式下显示空状态，提示用户登录
    this.setData({
      conversationList: [],
      systemMessages: [],
      unreadCount: 0
    })
  },

  /**
   * 显示登录组件
   */
  showLoginComponent() {
    // 显示登录框
    userUtils.showLoginComponent(this, true)
  },

  // 处理登录成功事件（登录组件调用）
  onLoginSuccess: function () {
    if (this.loginController) {
      this.loginController.handleLoginSuccess()
    }
  },

  /**
   * 登录失败处理
   */
  onLoginFail(e) {
    // 使用统一的错误处理
    if (e?.detail?.error) {
      userUtils.handleLoginError(e.detail.error)
    } else {
      wx.showToast({
        title: '登录失败',
        icon: 'none',
      })
    }
  },

  // 点击底部 tabBar 消息按钮时触发
  onTabItemTap: function(item) {
    // 如果已经在消息页面，重新刷新数据
    if (item.pagePath === 'pages/message/index') {
      console.log('重新刷新消息页面数据')

      // 防止重复加载：如果正在加载中，则跳过
      if (this.data.isLoading) {
        console.log('正在加载中，跳过重复刷新')
        return
      }

      // 重置搜索状态
      this.setData({
        searchKeyword: ''
      })

      // 如果已登录，使用手动刷新动画
      if (app.globalData.hasLogin) {
        this.manualRefresh()
      }
    }
  },
})